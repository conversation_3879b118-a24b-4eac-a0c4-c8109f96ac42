<?php
/**
 * Landing Page - Onoranze Funebri Ignagni Ceprano
 * Template moderno ed elegante per servizi funebri
 *
 * @package NextNaked
 */

// Includi il file di configurazione
require_once get_template_directory() . '/inc/landing-config.php';

// <PERSON><PERSON>eni i dati di configurazione
$contact_info = get_contact_info();
$services = get_services();
$company_values = get_company_values();
$key_messages = get_key_messages();
$hero_features = get_hero_features();
$opening_hours = get_opening_hours();
$social_links = get_social_links();

get_header();
?>

<!-- Loading Screen -->
<div id="loading-screen" class="loading-screen">
    <div class="loading-content">
        <div class="logo-container">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/logo.webp" alt="Onoranze Funebri Ignagni" class="loading-logo">
        </div>
        <div class="loading-spinner"></div>
        <p class="loading-text">Caricamento...</p>
    </div>
</div>

<!-- Progress Bar -->
<div class="scroll-progress">
    <div class="progress-bar"></div>
</div>

<main class="main-content">

<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles"></div>
    </div>

    <div class="hero-content">
        <div class="container">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="title-line"><?php echo $key_messages['hero_title']; ?></span>
                </h1>
                <p class="hero-subtitle">
                    <?php echo $key_messages['hero_subtitle']; ?>
                </p>
                <div class="hero-actions">
                    <a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>" class="btn btn-primary">
                        <i class="fas fa-phone"></i>
                        Contattaci Subito
                    </a>
                    <a href="#servizi" class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        I Nostri Servizi
                    </a>
                </div>
                <div class="hero-features">
                    <?php foreach ($hero_features as $feature): ?>
                    <div class="feature-item">
                        <i class="<?php echo $feature['icon']; ?>"></i>
                        <span><?php echo $feature['text']; ?></span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="servizi" class="services-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">I Nostri Servizi</h2>
            <p class="section-subtitle">
                Offriamo un servizio completo e professionale per accompagnarvi
                in ogni momento con rispetto e dignità
            </p>
        </div>

        <div class="services-grid">
            <?php foreach ($services as $index => $service): ?>
            <div class="service-card" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                <div class="service-icon">
                    <i class="<?php echo $service['icon']; ?>"></i>
                </div>
                <h3 class="service-title"><?php echo $service['title']; ?></h3>
                <p class="service-description">
                    <?php echo $service['description']; ?>
                </p>
                <div class="service-features">
                    <?php foreach ($service['features'] as $feature): ?>
                    <span><?php echo $feature; ?></span>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- About Section -->
<section id="chi-siamo" class="about-section">
    <div class="container">
        <div class="about-content">
            <div class="about-text" data-aos="fade-right">
                <div class="section-header">
                    <h2 class="section-title">Chi Siamo</h2>
                    <p class="section-subtitle">
                        Una storia di famiglia al servizio delle famiglie di Ceprano
                    </p>
                </div>

                <div class="about-story">
                    <p class="about-paragraph">
                        <?php echo $key_messages['about_intro']; ?>
                    </p>

                    <p class="about-paragraph">
                        <?php echo $key_messages['about_experience']; ?>
                    </p>

                    <div class="about-values">
                        <?php foreach ($company_values as $value): ?>
                        <div class="value-item">
                            <div class="value-icon">
                                <i class="<?php echo $value['icon']; ?>"></i>
                            </div>
                            <div class="value-content">
                                <h4><?php echo $value['title']; ?></h4>
                                <p><?php echo $value['description']; ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <div class="about-image" data-aos="fade-left">
                <div class="image-container">
                    <img src="<?php echo get_template_directory_uri(); ?>/assets/img/images/La città e le sue metamorfosi.webp"
                         alt="Panorama di Ceprano" class="about-img">
                    <div class="image-overlay">
                        <div class="overlay-content">
                            <h3>Ceprano</h3>
                            <p>La nostra città, le nostre radici</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contatti" class="contact-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Contatti</h2>
            <p class="section-subtitle">
                <?php echo $key_messages['contact_subtitle']; ?>
            </p>
        </div>

        <div class="contact-content">
            <div class="contact-info" data-aos="fade-right">
                <div class="contact-card">
                    <div class="contact-header">
                        <h3>Informazioni di Contatto</h3>
                        <p>Servizio attivo 24 ore su 24, 7 giorni su 7</p>
                    </div>

                    <div class="contact-details">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Telefono</h4>
                                <a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>"><?php echo COMPANY_PHONE; ?></a>
                                <span class="contact-note">Attivo 24/7</span>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Cellulare</h4>
                                <a href="tel:<?php echo str_replace(' ', '', COMPANY_MOBILE); ?>"><?php echo COMPANY_MOBILE; ?></a>
                                <span class="contact-note">Emergenze</span>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Email</h4>
                                <a href="mailto:<?php echo COMPANY_EMAIL; ?>"><?php echo COMPANY_EMAIL; ?></a>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Indirizzo</h4>
                                <address>
                                    <?php echo $contact_info['full_address']; ?>
                                </address>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Orari Ufficio</h4>
                                <div class="opening-hours">
                                    <div>Lun - Ven: <?php echo $opening_hours['lunedi_venerdi']; ?></div>
                                    <div>Sabato: <?php echo $opening_hours['sabato']; ?></div>
                                    <div>Domenica: <?php echo $opening_hours['domenica']; ?></div>
                                    <div class="emergency-note">
                                        <strong><?php echo $opening_hours['emergenze']; ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="contact-form-container" data-aos="fade-left">
                <div class="contact-form-card">
                    <h3>Richiedi Informazioni</h3>
                    <p>Compila il form per ricevere maggiori informazioni sui nostri servizi</p>

                    <form class="contact-form" id="contact-form">
                        <div class="form-group">
                            <label for="name">Nome e Cognome *</label>
                            <input type="text" id="name" name="name" required>
                            <span class="form-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="phone">Telefono *</label>
                            <input type="tel" id="phone" name="phone" required>
                            <span class="form-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email">
                            <span class="form-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="service">Servizio di Interesse</label>
                            <select id="service" name="service">
                                <option value="">Seleziona un servizio</option>
                                <option value="funebre-completo">Servizio Funebre Completo</option>
                                <option value="cremazione">Cremazione</option>
                                <option value="trasporti">Trasporti Funebri</option>
                                <option value="fiori">Fiori e Composizioni</option>
                                <option value="pratiche">Pratiche Burocratiche</option>
                                <option value="altro">Altro</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message">Messaggio</label>
                            <textarea id="message" name="message" rows="4"
                                      placeholder="Descrivi brevemente la tua richiesta..."></textarea>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="privacy" name="privacy" required>
                                <span class="checkmark"></span>
                                Accetto il trattamento dei dati personali secondo la
                                <a href="#privacy-policy" target="_blank">Privacy Policy</a> *
                            </label>
                            <span class="form-error"></span>
                        </div>

                        <button type="submit" class="btn btn-primary btn-submit">
                            <span class="btn-text">Invia Richiesta</span>
                            <span class="btn-loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                Invio in corso...
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Map Section -->
        <div class="map-section" data-aos="fade-up">
            <div class="map-container">
                <div class="map-overlay">
                    <div class="map-info">
                        <h4><?php echo COMPANY_NAME; ?></h4>
                        <p><?php echo $contact_info['address']; ?></p>
                        <a href="<?php echo get_google_maps_link(); ?>" target="_blank" class="map-link">
                            <i class="fas fa-external-link-alt"></i>
                            Apri in Google Maps
                        </a>
                    </div>
                </div>
                <iframe
                    src="<?php echo get_google_maps_embed(); ?>"
                    width="100%"
                    height="400"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </div>
    </div>
</section>

</main>

<?php get_footer(); ?>
